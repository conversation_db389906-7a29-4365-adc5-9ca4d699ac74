// MongoDB初始化脚本
// 这个脚本会在MongoDB容器首次启动时执行

// 切换到应用数据库
db = db.getSiblingDB('deepchat')

// 创建应用用户（可选）
// db.createUser({
//   user: 'deepchat_user',
//   pwd: 'deepchat_password',
//   roles: [
//     {
//       role: 'readWrite',
//       db: 'deepchat'
//     }
//   ]
// });

// 创建集合和索引
db.createCollection('users')
db.createCollection('files')

// 为用户集合创建索引
db.users.createIndex({ username: 1 }, { unique: true })
db.users.createIndex({ email: 1 }, { unique: true })
db.users.createIndex({ createdAt: -1 })

// 为文件集合创建索引
db.files.createIndex({ filename: 1 })
db.files.createIndex({ md5: 1 }, { unique: true })
db.files.createIndex({ uploadedBy: 1 })
db.files.createIndex({ createdAt: -1 })

print('数据库初始化完成')
