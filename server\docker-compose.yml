version: '3.8'

services:
  # MongoDB数据库服务
  mongodb:
    image: mongo:7.0
    container_name: deepchat-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: ${MONGO_DATABASE:-deepchat}
    ports:
      - "${MONGO_PORT:-27017}:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - deepchat-network

  # DeepChat后端服务
  deepchat-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deepchat-server
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: ${SERVER_PORT:-3000}
      MONGODB_URI: mongodb://${MONGO_ROOT_USERNAME:-admin}:${MONGO_ROOT_PASSWORD:-password}@mongodb:27017/${MONGO_DATABASE:-deepchat}?authSource=admin
      JWT_SECRET: ${JWT_SECRET}
      FILE_SIZE_LIMIT: ${FILE_SIZE_LIMIT:-50mb}
      UPLOAD_DIR: ${UPLOAD_DIR:-uploads}
    ports:
      - "${SERVER_PORT:-3000}:${SERVER_PORT:-3000}"
    volumes:
      - ./uploads:/app/uploads
      - ./temp:/app/temp
    depends_on:
      - mongodb
    networks:
      - deepchat-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${SERVER_PORT:-3000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: deepchat-nginx
    restart: unless-stopped
    ports:
      - "${NGINX_PORT:-80}:80"
      - "${NGINX_SSL_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - deepchat-server
    networks:
      - deepchat-network
    profiles:
      - nginx

volumes:
  mongodb_data:
    driver: local

networks:
  deepchat-network:
    driver: bridge
